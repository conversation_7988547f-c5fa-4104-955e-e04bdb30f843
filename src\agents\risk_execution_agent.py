import asyncio
import logging
import time
import base64
import hashlib
import hmac
import urllib.parse
from collections import defaultdict
from typing import Dict, List, Tuple, Optional, Any

# Import Windows-compatible HTTP client
from src.utils.http_client import http_client
from src.utils.pnl_tracker import pnl_tracker

logger = logging.getLogger(__name__)

class KrakenAPIClient:
    """
    Kraken API client wrapper with rate limiting and error handling.

    This uses the actual Kraken REST API for LIVE TRADING.
    ⚠️  REAL MONEY AT RISK ⚠️
    """

    def __init__(self, api_key: str, api_secret: str):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.kraken.com"
        self.status = "ACTIVE"  # ACTIVE, QUARANTINED, DISABLED
        self.quarantine_until = 0  # Timestamp when quarantine ends
        self.last_api_call_time = 0  # For rate limiting
        self.api_call_cost = 0  # Accumulated cost within a window
        self.call_count = 0  # Total API calls made

        logger.info(f"KrakenAPIClient initialized for LIVE TRADING with key: {api_key[:8]}...")
        logger.warning("WARNING: LIVE TRADING MODE - REAL MONEY AT RISK")

    def _get_kraken_signature(self, urlpath: str, data: dict, secret: str) -> str:
        """Generate Kraken API signature"""
        postdata = urllib.parse.urlencode(data)
        encoded = (str(data['nonce']) + postdata).encode()
        message = urlpath.encode() + hashlib.sha256(encoded).digest()

        mac = hmac.new(base64.b64decode(secret), message, hashlib.sha512)
        sigdigest = base64.b64encode(mac.digest())
        return sigdigest.decode()

    async def _make_api_request(self, endpoint: str, data: dict = None, private: bool = False) -> dict:
        """Make authenticated API request to Kraken"""
        if data is None:
            data = {}

        url = f"{self.base_url}{endpoint}"
        headers = {'User-Agent': 'Kraken Multi-Agent Trading System'}

        if private:
            # Add nonce for private endpoints
            data['nonce'] = str(int(1000 * time.time()))

            # Generate signature
            signature = self._get_kraken_signature(endpoint, data, self.api_secret)
            headers.update({
                'API-Key': self.api_key,
                'API-Sign': signature
            })

        self.call_count += 1
        self.last_api_call_time = time.time()

        # Use Windows-compatible HTTP client
        method = 'POST' if private else 'GET'
        result = await http_client.make_request(method, url, headers, data if private else None)

        if result.get('error'):
            raise Exception(f"Kraken API Error: {result['error']}")

        return result.get('result', {})

    async def get_ohlc(self, pair: str, interval: int = 1) -> Dict[str, Any]:
        """Get OHLC data using live Kraken API"""
        try:
            data = {
                'pair': pair,
                'interval': str(interval)
            }
            result = await self._make_api_request('/0/public/OHLC', data, private=False)
            return {"result": result}
        except Exception as e:
            logger.error(f"Failed to get OHLC data for {pair}: {e}")
            return {"result": {}}

    async def get_account_balance(self) -> Dict[str, float]:
        """Get account balance from live Kraken API"""
        try:
            result = await self._make_api_request('/0/private/Balance', private=True)

            # Convert Kraken balance format to our format
            balances = {}
            for asset, balance_str in result.items():
                balance = float(balance_str)
                if balance > 0:  # Only include non-zero balances
                    balances[asset] = balance

            logger.info(f"Retrieved live account balance: {len(balances)} assets")
            return balances

        except Exception as e:
            logger.error(f"Failed to get account balance: {e}")
            # Fallback to prevent system crash
            return {"USD": 0.0}

    async def place_order(self, pair: str, type: str, ordertype: str,
                         volume: float, price: Optional[float] = None,
                         leverage: Optional[int] = None) -> Dict[str, Any]:
        """Place a trading order using live Kraken API"""
        try:
            data = {
                'pair': pair,
                'type': type,
                'ordertype': ordertype,
                'volume': str(volume)
            }

            if price and ordertype == 'limit':
                data['price'] = str(price)

            if leverage:
                data['leverage'] = str(leverage)

            result = await self._make_api_request('/0/private/AddOrder', data, private=True)

            order_desc = f"{ordertype} {type} {volume} {pair}"
            if price:
                order_desc += f" @ {price}"

            logger.info(f"[LIVE TRADING] Order placed: {order_desc}")
            logger.warning("WARNING: REAL MONEY ORDER PLACED")

            return result

        except Exception as e:
            logger.error(f"Failed to place order: {e}")
            raise

    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel an order using live Kraken API"""
        try:
            data = {'txid': order_id}
            result = await self._make_api_request('/0/private/CancelOrder', data, private=True)
            logger.info(f"[LIVE TRADING] Order cancelled: {order_id}")
            return result
        except Exception as e:
            logger.error(f"Failed to cancel order {order_id}: {e}")
            raise

    async def get_open_orders(self) -> Dict[str, Any]:
        """Get open orders using live Kraken API"""
        try:
            result = await self._make_api_request('/0/private/OpenOrders', private=True)
            return result
        except Exception as e:
            logger.error(f"Failed to get open orders: {e}")
            return {"open": {}}

    async def get_closed_orders(self, start: Optional[int] = None) -> Dict[str, Any]:
        """Get closed orders using live Kraken API"""
        try:
            data = {}
            if start:
                data['start'] = str(start)
            result = await self._make_api_request('/0/private/ClosedOrders', data, private=True)
            return result
        except Exception as e:
            logger.error(f"Failed to get closed orders: {e}")
            return {"closed": {}}

    async def get_trades_history(self, start: Optional[int] = None) -> Dict[str, Any]:
        """Get trades history using live Kraken API"""
        try:
            data = {}
            if start:
                data['start'] = str(start)
            result = await self._make_api_request('/0/private/TradesHistory', data, private=True)
            return result
        except Exception as e:
            logger.error(f"Failed to get trades history: {e}")
            return {"trades": {}}



class RiskExecutionAgent:
    """
    Risk & Execution Agent for managing trading orders and risk controls.
    
    Responsibilities:
    - Process trading signals from Strategy & Signal Agent
    - Implement risk management controls (position sizing, drawdown limits)
    - Execute orders through Kraken API with intelligent key rotation
    - Monitor portfolio equity and trigger circuit breakers
    - Handle API rate limiting and error recovery
    """
    
    def __init__(self, signal_queue: asyncio.Queue, dashboard_queue: asyncio.Queue,
                 kraken_api_keys: List[Tuple[str, str]], max_portfolio_drawdown: float = 0.50,
                 api_quarantine_time: int = 300, api_rate_limit_window: int = 60,
                 api_max_cost: int = 100):

        self.signal_queue = signal_queue
        self.dashboard_queue = dashboard_queue

        # Initialize API clients for LIVE TRADING
        self.kraken_api_clients = [
            KrakenAPIClient(key, secret)
            for key, secret in kraken_api_keys
        ]
        self.current_api_key_index = 0
        
        # Risk management
        self.max_portfolio_drawdown = max_portfolio_drawdown
        self.initial_portfolio_equity: Optional[float] = None
        self.current_portfolio_equity = 0.0
        self.max_position_size = 0.1  # 10% of portfolio per position
        
        # Trading state
        self.open_positions = defaultdict(lambda: {
            "volume": 0.0, 
            "avg_entry_price": 0.0, 
            "unrealized_pnl": 0.0
        })
        self.open_orders: Dict[str, Dict] = {}
        self.trading_halted = False
        self.cool_down_active = False
        self.cool_down_until = 0
        
        # API management
        self.api_quarantine_time = api_quarantine_time
        self.api_rate_limit_window = api_rate_limit_window
        self.api_max_cost = api_max_cost
        self.api_call_history: List[Tuple[float, int]] = []  # (timestamp, cost)
        
        # Statistics
        self.total_trades = 0
        self.successful_trades = 0
        self.failed_trades = 0
        
        self.is_running = False

        logger.info(f"RiskExecutionAgent initialized for LIVE TRADING with "
                   f"{len(self.kraken_api_clients)} API keys")
        logger.warning("WARNING: LIVE TRADING MODE - REAL MONEY AT RISK")

    async def _get_active_kraken_client(self) -> Optional[KrakenAPIClient]:
        """Get an active Kraken API client using intelligent rotation"""
        current_time = time.time()
        
        # Check for clients coming out of quarantine
        for client in self.kraken_api_clients:
            if client.status == "QUARANTINED" and client.quarantine_until < current_time:
                client.status = "ACTIVE"
                logger.info(f"API Key {client.api_key[:8]}... reactivated from quarantine")
                await self.dashboard_queue.put({
                    "type": "api_status_update", 
                    "key": client.api_key[:8], 
                    "status": "ACTIVE"
                })

        # Get active clients
        active_clients = [
            client for client in self.kraken_api_clients 
            if client.status == "ACTIVE"
        ]
        
        if not active_clients:
            logger.error("No active API clients available. All keys are quarantined or disabled.")
            return None

        # Round-robin selection among active clients
        attempts = 0
        while attempts < len(self.kraken_api_clients):
            client = self.kraken_api_clients[self.current_api_key_index]
            self.current_api_key_index = (self.current_api_key_index + 1) % len(self.kraken_api_clients)
            
            if client.status == "ACTIVE":
                return client
                
            attempts += 1

        return None

    async def _manage_api_rate_limit(self, cost: int = 1):
        """Manage API rate limiting to prevent exceeding limits"""
        current_time = time.time()
        
        # Clean up old entries
        self.api_call_history = [
            (timestamp, call_cost) for timestamp, call_cost in self.api_call_history
            if current_time - timestamp < self.api_rate_limit_window
        ]
        
        # Calculate current cost within window
        current_cost = sum(call_cost for _, call_cost in self.api_call_history)
        
        if current_cost + cost > self.api_max_cost:
            if self.api_call_history:
                oldest_call_time = self.api_call_history[0][0]
                wait_time = self.api_rate_limit_window - (current_time - oldest_call_time)
                wait_time = max(wait_time, 1.0)  # Minimum 1 second wait
                
                logger.warning(f"API rate limit approaching. Waiting {wait_time:.1f} seconds...")
                await asyncio.sleep(wait_time)
                
                # Clean up again after waiting
                current_time = time.time()
                self.api_call_history = [
                    (timestamp, call_cost) for timestamp, call_cost in self.api_call_history
                    if current_time - timestamp < self.api_rate_limit_window
                ]
        
        # Record this API call
        self.api_call_history.append((current_time, cost))

    async def _execute_api_call(self, api_method, *args, **kwargs) -> Any:
        """Execute API call with retry logic and error handling"""
        max_retries = len(self.kraken_api_clients)
        
        for attempt in range(max_retries):
            client = await self._get_active_kraken_client()
            if not client:
                raise Exception("No active API clients available")

            # Manage rate limiting
            await self._manage_api_rate_limit(1)

            try:
                # Execute the API call
                result = await api_method(client, *args, **kwargs)
                return result
                
            except Exception as e:
                error_msg = str(e)
                logger.warning(f"API call failed with key {client.api_key[:8]}...: {error_msg}")
                
                # Handle different types of errors
                if any(keyword in error_msg.lower() for keyword in ["nonce", "rate limit", "timeout"]):
                    # Temporary error - quarantine the key
                    client.status = "QUARANTINED"
                    client.quarantine_until = time.time() + self.api_quarantine_time
                    logger.warning(f"API Key {client.api_key[:8]}... quarantined until "
                                 f"{time.ctime(client.quarantine_until)}")
                    await self.dashboard_queue.put({
                        "type": "api_status_update",
                        "key": client.api_key[:8],
                        "status": "QUARANTINED",
                        "quarantine_until": client.quarantine_until,
                        "reason": error_msg
                    })
                else:
                    # Fatal error - disable the key
                    client.status = "DISABLED"
                    logger.error(f"API Key {client.api_key[:8]}... disabled due to fatal error: {error_msg}")
                    await self.dashboard_queue.put({
                        "type": "api_status_update",
                        "key": client.api_key[:8],
                        "status": "DISABLED",
                        "reason": error_msg
                    })

                # If this was the last attempt, re-raise the exception
                if attempt == max_retries - 1:
                    raise Exception(f"All API keys failed after {max_retries} attempts: {error_msg}")
                else:
                    logger.info(f"Retrying with next API key (attempt {attempt + 2}/{max_retries})...")
                    await asyncio.sleep(1)  # Brief delay before retry

        raise Exception("Unexpected error in API call execution")

    async def _get_current_prices(self) -> Dict[str, float]:
        """Get current market prices for major assets"""
        try:
            # Get ticker data for major pairs
            client = await self._get_active_kraken_client()
            if not client:
                return {}

            # Get BTC and ETH prices
            btc_result = await client._make_api_request('/0/public/Ticker?pair=XBTUSD', private=False)
            eth_result = await client._make_api_request('/0/public/Ticker?pair=ETHUSD', private=False)

            prices = {}

            # Parse BTC price
            if 'XXBTZUSD' in btc_result:
                prices['XBT'] = float(btc_result['XXBTZUSD']['c'][0])
            elif 'XBTUSD' in btc_result:
                prices['XBT'] = float(btc_result['XBTUSD']['c'][0])

            # Parse ETH price
            if 'XETHZUSD' in eth_result:
                prices['ETH'] = float(eth_result['XETHZUSD']['c'][0])
            elif 'ETHUSD' in eth_result:
                prices['ETH'] = float(eth_result['ETHUSD']['c'][0])

            logger.debug(f"Current prices: {prices}")
            return prices

        except Exception as e:
            logger.error(f"Failed to get current prices: {e}")
            # Fallback prices
            return {'XBT': 45000.0, 'ETH': 2500.0}

    async def _update_portfolio_equity(self):
        """Update current portfolio equity from account balances"""
        try:
            balances = await self._execute_api_call(KrakenAPIClient.get_account_balance)
            current_prices = await self._get_current_prices()

            # Calculate total equity using real market prices
            total_equity = 0.0

            for asset, balance in balances.items():
                if asset == "USD" or asset == "ZUSD":
                    total_equity += balance
                elif asset == "XBT" or asset == "XXBT":
                    btc_price = current_prices.get('XBT', 45000.0)
                    total_equity += balance * btc_price
                elif asset == "ETH" or asset == "XETH":
                    eth_price = current_prices.get('ETH', 2500.0)
                    total_equity += balance * eth_price
                # Add more assets as needed

            self.current_portfolio_equity = total_equity

            if self.initial_portfolio_equity is None:
                self.initial_portfolio_equity = total_equity
                logger.info(f"Initial portfolio equity set: ${total_equity:.2f}")

            # Update P&L tracker with current prices
            for asset, price in current_prices.items():
                if asset == 'XBT':
                    pnl_tracker.update_price('XBTUSD', price)
                elif asset == 'ETH':
                    pnl_tracker.update_price('ETHUSD', price)

            # Get P&L metrics
            pnl_metrics = pnl_tracker.get_performance_metrics()
            total_pnl = pnl_metrics['total_pnl']

            logger.info(f"Current portfolio equity: ${total_equity:.2f} (from live balances)")
            logger.info(f"Total P&L: ${total_pnl:.2f} (Realized: ${pnl_metrics['total_realized_pnl']:.2f}, "
                       f"Unrealized: ${pnl_metrics['total_unrealized_pnl']:.2f})")

            await self.dashboard_queue.put({
                "type": "equity_update",
                "value": total_equity,
                "balances": balances,
                "pnl_metrics": pnl_metrics
            })

        except Exception as e:
            logger.error(f"Failed to update portfolio equity: {e}")

    async def _check_master_circuit_breaker(self):
        """Check if master circuit breaker should be triggered"""
        if self.initial_portfolio_equity is None or self.initial_portfolio_equity == 0:
            return

        drawdown_limit_value = self.initial_portfolio_equity * (1 - self.max_portfolio_drawdown)
        current_drawdown = (self.initial_portfolio_equity - self.current_portfolio_equity) / self.initial_portfolio_equity

        if self.current_portfolio_equity <= drawdown_limit_value:
            logger.critical(f"MASTER CIRCUIT BREAKER TRIGGERED! "
                          f"Equity: ${self.current_portfolio_equity:.2f} <= "
                          f"Limit: ${drawdown_limit_value:.2f} "
                          f"(Drawdown: {current_drawdown:.1%})")

            self.trading_halted = True
            await self.dashboard_queue.put({
                "type": "system_alert",
                "level": "CRITICAL",
                "message": "MASTER CIRCUIT BREAKER TRIGGERED! Trading halted.",
                "drawdown": current_drawdown,
                "equity": self.current_portfolio_equity,
                "limit": drawdown_limit_value
            })

            await self._execute_total_halt_actions()

    async def _execute_total_halt_actions(self):
        """Execute emergency halt actions: cancel orders and close positions"""
        logger.info("Executing total halt actions...")

        try:
            # Cancel all open orders
            open_orders = await self._execute_api_call(KrakenAPIClient.get_open_orders)
            cancelled_orders = 0

            for order_id in open_orders.get("open", {}):
                try:
                    await self._execute_api_call(KrakenAPIClient.cancel_order, order_id)
                    cancelled_orders += 1
                    logger.info(f"Emergency cancelled order: {order_id}")
                except Exception as e:
                    logger.error(f"Failed to cancel order {order_id}: {e}")

            # Close all positions (simplified - market orders)
            closed_positions = 0
            for pair, position_info in self.open_positions.items():
                volume = position_info["volume"]
                if volume > 0:
                    try:
                        await self._execute_api_call(
                            KrakenAPIClient.place_order,
                            pair, "sell", "market", volume=volume
                        )
                        closed_positions += 1
                        logger.info(f"Emergency closed position: {pair} volume: {volume}")
                    except Exception as e:
                        logger.error(f"Failed to close position {pair}: {e}")

            # Clear internal state
            self.open_positions.clear()
            self.open_orders.clear()

            await self.dashboard_queue.put({
                "type": "system_alert",
                "level": "INFO",
                "message": f"Emergency halt completed. Cancelled {cancelled_orders} orders, "
                          f"closed {closed_positions} positions."
            })

        except Exception as e:
            logger.error(f"Error during total halt actions: {e}")
            await self.dashboard_queue.put({
                "type": "system_alert",
                "level": "ERROR",
                "message": f"Error during emergency halt: {e}"
            })

    async def _check_volatility_halt(self, pair: str, current_price: float):
        """Check if volatility halt should be triggered for a pair"""
        try:
            # Get recent price data to calculate volatility
            ohlc_data = await self._execute_api_call(KrakenAPIClient.get_ohlc, pair, 1)
            recent_prices = []

            for candle in ohlc_data.get("result", {}).get(pair, [])[-10:]:
                recent_prices.append(float(candle[4]))  # Close price

            if len(recent_prices) >= 2:
                price_change = abs(current_price - recent_prices[-2]) / recent_prices[-2]

                if price_change > 0.08 and not self.cool_down_active:  # 8% price change
                    logger.warning(f"VOLATILITY HALT TRIGGERED for {pair}! "
                                 f"Price change: {price_change:.1%}")

                    self.cool_down_active = True
                    self.cool_down_until = time.time() + (15 * 60)  # 15 minutes

                    await self.dashboard_queue.put({
                        "type": "system_alert",
                        "level": "WARNING",
                        "message": f"VOLATILITY HALT for {pair}! Cooling down for 15 minutes.",
                        "price_change": price_change,
                        "current_price": current_price
                    })

        except Exception as e:
            logger.error(f"Error checking volatility for {pair}: {e}")

    async def _is_cool_down_active(self) -> bool:
        """Check if cool-down period is active"""
        if self.cool_down_active and time.time() < self.cool_down_until:
            return True
        elif self.cool_down_active:
            self.cool_down_active = False
            logger.info("Cool-down period ended. Trading resumed.")
            await self.dashboard_queue.put({
                "type": "system_alert",
                "level": "INFO",
                "message": "Cool-down period ended. Trading resumed."
            })
        return False

    async def _calculate_position_size(self, signal: Dict[str, Any]) -> float:
        """Calculate appropriate position size based on risk management rules"""
        try:
            risk_percentage = signal.get("risk_percentage", 0.01)
            pair = signal["pair"]
            price = signal["price"]

            # Calculate risk amount
            risk_amount = self.current_portfolio_equity * risk_percentage

            # Calculate position size based on risk amount
            # For simplicity, assume 1% stop loss
            stop_loss_percentage = 0.01
            position_value = risk_amount / stop_loss_percentage
            volume = position_value / price

            # Apply maximum position size limit
            max_position_value = self.current_portfolio_equity * self.max_position_size
            max_volume = max_position_value / price

            final_volume = min(volume, max_volume)

            logger.info(f"Position sizing for {pair}: "
                       f"Risk: ${risk_amount:.2f} ({risk_percentage:.1%}), "
                       f"Volume: {final_volume:.6f}")

            return final_volume

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0

    async def process_signal(self, signal: Dict[str, Any]):
        """Process a trading signal and execute order if conditions are met"""
        if self.trading_halted:
            logger.warning(f"Trading halted. Rejecting signal: {signal}")
            return

        if await self._is_cool_down_active():
            logger.warning(f"Cool-down active. Rejecting signal: {signal}")
            return

        logger.info(f"Processing signal: {signal['direction'].upper()} {signal['pair']} at {signal['price']}")

        try:
            # Calculate position size
            volume = await self._calculate_position_size(signal)
            if volume <= 0:
                logger.warning(f"Calculated volume is zero or negative. Rejecting signal.")
                return

            # Check circuit breaker before placing order
            await self._check_master_circuit_breaker()
            if self.trading_halted:
                logger.warning(f"Circuit breaker triggered. Rejecting signal.")
                return

            # Check volatility
            await self._check_volatility_halt(signal["pair"], signal["price"])
            if await self._is_cool_down_active():
                logger.warning(f"Volatility halt triggered. Rejecting signal.")
                return

            # Place the order
            order_type = signal.get("direction", "buy")  # buy or sell
            ordertype = "market"  # Could be configurable

            response = await self._execute_api_call(
                KrakenAPIClient.place_order,
                signal["pair"],
                order_type,
                ordertype,
                volume=volume,
                price=signal["price"] if ordertype == "limit" else None
            )

            order_id = response["txid"][0]

            # Store order information
            self.open_orders[order_id] = {
                "pair": signal["pair"],
                "type": order_type,
                "ordertype": ordertype,
                "volume": volume,
                "price": signal["price"],
                "status": "pending",
                "strategy_id": signal["strategy_id"],
                "timestamp": time.time()
            }

            self.total_trades += 1
            self.successful_trades += 1

            logger.info(f"Order placed successfully: {order_id} - "
                       f"{order_type.upper()} {volume:.6f} {signal['pair']}")

            # Update dashboard
            await self.dashboard_queue.put({
                "type": "order_update",
                "order_id": order_id,
                "status": "placed",
                "details": self.open_orders[order_id]
            })

            # Update position tracking with P&L integration
            if ordertype == "market":
                # Update P&L tracker with new position/trade
                if order_type == "buy":
                    # Opening or adding to long position
                    pnl_tracker.open_position(
                        pair=signal["pair"],
                        side="long",
                        volume=volume,
                        entry_price=signal["price"]
                    )

                    # Update legacy position tracking
                    self.open_positions[signal["pair"]]["volume"] += volume
                    current_pos = self.open_positions[signal["pair"]]
                    if current_pos["volume"] > 0:
                        total_value = (current_pos["avg_entry_price"] * (current_pos["volume"] - volume) +
                                     signal["price"] * volume)
                        current_pos["avg_entry_price"] = total_value / current_pos["volume"]

                else:  # sell
                    # Closing or reducing long position
                    if self.open_positions[signal["pair"]]["volume"] > 0:
                        # Close position in P&L tracker
                        trade = pnl_tracker.close_position(
                            pair=signal["pair"],
                            volume=volume,
                            exit_price=signal["price"]
                        )

                        if trade:
                            logger.info(f"Trade completed: {trade.pnl:.2f} P&L")

                    # Update legacy position tracking
                    self.open_positions[signal["pair"]]["volume"] -= volume
                    if self.open_positions[signal["pair"]]["volume"] <= 0:
                        self.open_positions[signal["pair"]]["volume"] = 0
                        self.open_positions[signal["pair"]]["avg_entry_price"] = 0

                await self.dashboard_queue.put({
                    "type": "position_update",
                    "pair": signal["pair"],
                    "details": dict(self.open_positions[signal["pair"]])
                })

        except Exception as e:
            self.failed_trades += 1
            logger.error(f"Failed to process signal {signal}: {e}")
            await self.dashboard_queue.put({
                "type": "order_update",
                "status": "failed",
                "signal": signal,
                "error": str(e)
            })

    async def run(self):
        """Main run loop for the Risk & Execution Agent"""
        self.is_running = True
        logger.info("RiskExecutionAgent starting...")

        # Start periodic tasks
        equity_check_task = asyncio.create_task(self._periodic_equity_check())

        try:
            # Main signal processing loop
            while self.is_running:
                try:
                    signal = await self.signal_queue.get()
                    await self.process_signal(signal)
                except Exception as e:
                    logger.error(f"Error in main signal processing loop: {e}")
                    await asyncio.sleep(1)  # Brief pause before continuing

        except Exception as e:
            logger.error(f"Fatal error in RiskExecutionAgent: {e}")
        finally:
            self.is_running = False
            equity_check_task.cancel()
            logger.info("RiskExecutionAgent stopped")

    async def stop(self):
        """Stop the Risk & Execution Agent"""
        logger.info("Stopping RiskExecutionAgent...")
        self.is_running = False

    async def _periodic_equity_check(self):
        """Periodically update portfolio equity and check circuit breakers"""
        while self.is_running:
            try:
                await self._update_portfolio_equity()
                await self._check_master_circuit_breaker()
                await asyncio.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in periodic equity check: {e}")
                await asyncio.sleep(60)

    def get_status(self) -> Dict[str, Any]:
        """Get current status of the Risk & Execution Agent"""
        return {
            "is_running": self.is_running,
            "trading_halted": self.trading_halted,
            "cool_down_active": self.cool_down_active,
            "current_equity": self.current_portfolio_equity,
            "initial_equity": self.initial_portfolio_equity,
            "max_drawdown": self.max_portfolio_drawdown,
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "failed_trades": self.failed_trades,
            "open_positions": dict(self.open_positions),
            "open_orders": len(self.open_orders),
            "active_api_keys": len([c for c in self.kraken_api_clients if c.status == "ACTIVE"])
        }

# Example usage and testing
async def main():
    """Example usage of RiskExecutionAgent"""
    signal_queue = asyncio.Queue()
    dashboard_queue = asyncio.Queue()

    # Test API keys (dummy)
    api_keys = [
        ("test_key_1", "test_secret_1"),
        ("test_key_2", "test_secret_2")
    ]

    # Initialize agent
    rea = RiskExecutionAgent(
        signal_queue,
        dashboard_queue,
        api_keys,
        max_portfolio_drawdown=0.50,
        is_sandbox=True
    )

    # Start agent
    agent_task = asyncio.create_task(rea.run())

    # Dashboard consumer
    async def dashboard_consumer():
        while True:
            try:
                update = await dashboard_queue.get()
                logger.info(f"[Dashboard] {update}")
            except Exception as e:
                logger.error(f"Dashboard consumer error: {e}")

    dashboard_task = asyncio.create_task(dashboard_consumer())

    # Simulate signals
    async def signal_generator():
        await asyncio.sleep(5)  # Wait for agent to start

        test_signals = [
            {
                "type": "entry",
                "direction": "buy",
                "pair": "XBT/USD",
                "price": 45000.0,
                "timestamp": time.time(),
                "strategy_id": "trend_following",
                "risk_percentage": 0.01
            },
            {
                "type": "entry",
                "direction": "buy",
                "pair": "ETH/USD",
                "price": 2500.0,
                "timestamp": time.time(),
                "strategy_id": "scalping",
                "risk_percentage": 0.005
            }
        ]

        for signal in test_signals:
            await signal_queue.put(signal)
            await asyncio.sleep(10)

    signal_task = asyncio.create_task(signal_generator())

    # Run for a while then stop
    try:
        await asyncio.sleep(60)  # Run for 1 minute
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await rea.stop()
        agent_task.cancel()
        dashboard_task.cancel()
        signal_task.cancel()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    asyncio.run(main())
