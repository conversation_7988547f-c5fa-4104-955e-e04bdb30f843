"""
Windows-compatible HTTP client wrapper for Kraken API calls.
Handles the aiodns SelectorEventLoop issue on Windows.
"""

import asyncio
import sys
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class WindowsCompatibleHTTPClient:
    """HTTP client that works reliably on Windows"""
    
    def __init__(self):
        self.use_aiohttp = sys.platform != "win32"
        
        if self.use_aiohttp:
            import aiohttp
            self.aiohttp = aiohttp
        else:
            import requests
            self.requests = requests
            logger.info("Using requests library for Windows compatibility")
    
    async def make_request(self, method: str, url: str, headers: Dict[str, str] = None, 
                          data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make HTTP request with Windows compatibility"""
        
        if headers is None:
            headers = {}
        
        if self.use_aiohttp:
            return await self._make_aiohttp_request(method, url, headers, data)
        else:
            return await self._make_requests_request(method, url, headers, data)
    
    async def _make_aiohttp_request(self, method: str, url: str, headers: Dict[str, str], 
                                   data: Dict[str, Any]) -> Dict[str, Any]:
        """Make request using aiohttp (Linux/Mac)"""
        async with self.aiohttp.ClientSession() as session:
            if method.upper() == 'POST':
                async with session.post(url, data=data, headers=headers) as response:
                    return await response.json()
            else:
                async with session.get(url, headers=headers) as response:
                    return await response.json()
    
    async def _make_requests_request(self, method: str, url: str, headers: Dict[str, str], 
                                    data: Dict[str, Any]) -> Dict[str, Any]:
        """Make request using requests library (Windows)"""
        # Run in thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        
        def sync_request():
            if method.upper() == 'POST':
                response = self.requests.post(url, data=data, headers=headers, timeout=30)
            else:
                response = self.requests.get(url, headers=headers, timeout=30)
            
            response.raise_for_status()
            return response.json()
        
        return await loop.run_in_executor(None, sync_request)

# Global instance
http_client = WindowsCompatibleHTTPClient()
