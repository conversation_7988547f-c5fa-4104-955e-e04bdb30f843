import logging
import logging.handlers
import os
from datetime import datetime
import asyncio
from typing import Optional

# Global log queue for dashboard agent
log_queue = asyncio.Queue()

class QueueHandler(logging.Handler):
    """Custom logging handler that puts log records into an asyncio queue"""
    
    def __init__(self, queue: asyncio.Queue):
        super().__init__()
        self.queue = queue
    
    def emit(self, record):
        try:
            # Put the log record into the queue (non-blocking)
            try:
                self.queue.put_nowait(self.format(record))
            except asyncio.QueueFull:
                # If queue is full, skip this log message to prevent blocking
                pass
        except Exception:
            self.handleError(record)

def setup_logging(
    log_level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_queue_handler: bool = True
) -> logging.Logger:
    """
    Set up comprehensive logging configuration for the trading system.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Path to log file (if None, uses default)
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        enable_queue_handler: Whether to enable queue handler for dashboard
    
    Returns:
        Configured logger instance
    """
    
    # Create logs directory if it doesn't exist
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Default log file name with timestamp
    if log_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"crypto_trading_{timestamp}.log")
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler with Windows encoding support
    import sys
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(formatter)

    # Set encoding for Windows compatibility
    if hasattr(console_handler.stream, 'reconfigure'):
        try:
            console_handler.stream.reconfigure(encoding='utf-8', errors='replace')
        except:
            pass  # Fallback if reconfigure fails

    logger.addHandler(console_handler)
    
    # File handler with rotation and UTF-8 encoding
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_file_size,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, log_level.upper()))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # Queue handler for dashboard (if enabled)
    if enable_queue_handler:
        queue_handler = QueueHandler(log_queue)
        queue_handler.setLevel(getattr(logging, log_level.upper()))
        queue_handler.setFormatter(formatter)
        logger.addHandler(queue_handler)
    
    logger.info(f"Logging initialized - Level: {log_level}, File: {log_file}")
    return logger

def get_log_queue() -> asyncio.Queue:
    """Get the global log queue for dashboard agent"""
    return log_queue
