"""
Real-time P&L Tracker for Live Trading
Calculates unrealized and realized profits/losses from live positions.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from decimal import Decimal, ROUND_HALF_UP

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """Represents a trading position"""
    pair: str
    side: str  # 'long' or 'short'
    volume: float
    avg_entry_price: float
    entry_time: float
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0

@dataclass
class Trade:
    """Represents a completed trade"""
    pair: str
    side: str
    volume: float
    entry_price: float
    exit_price: float
    entry_time: float
    exit_time: float
    pnl: float
    fees: float = 0.0

class RealTimePnLTracker:
    """
    Real-time P&L tracking system for live trading positions.
    
    Features:
    - Track open positions with unrealized P&L
    - Calculate realized P&L from closed trades
    - Real-time portfolio performance metrics
    - Risk metrics (drawdown, win rate, etc.)
    """
    
    def __init__(self):
        self.positions: Dict[str, Position] = {}
        self.trades: List[Trade] = []
        self.current_prices: Dict[str, float] = {}
        
        # Performance metrics
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
        self.peak_equity = 0.0
        self.max_drawdown = 0.0
        self.win_count = 0
        self.loss_count = 0
        
        # Risk metrics
        self.daily_pnl: List[Tuple[float, float]] = []  # (timestamp, pnl)
        self.max_position_value = 0.0
        
        logger.info("RealTimePnLTracker initialized")
    
    def update_price(self, pair: str, price: float):
        """Update current market price for a trading pair"""
        self.current_prices[pair] = price
        
        # Update unrealized P&L for open positions
        if pair in self.positions:
            self._calculate_unrealized_pnl(pair)
    
    def open_position(self, pair: str, side: str, volume: float, entry_price: float):
        """Open a new position or add to existing position"""
        current_time = time.time()
        
        if pair in self.positions:
            # Add to existing position (average down/up)
            existing = self.positions[pair]
            
            if existing.side == side:
                # Same side - average the entry price
                total_volume = existing.volume + volume
                total_cost = (existing.volume * existing.avg_entry_price) + (volume * entry_price)
                new_avg_price = total_cost / total_volume
                
                existing.volume = total_volume
                existing.avg_entry_price = new_avg_price
                
                logger.info(f"Added to {side} position in {pair}: "
                          f"{volume:.8f} @ {entry_price:.2f} "
                          f"(new avg: {new_avg_price:.2f}, total: {total_volume:.8f})")
            else:
                # Opposite side - close partial or full position
                self._close_partial_position(pair, volume, entry_price, current_time)
        else:
            # New position
            self.positions[pair] = Position(
                pair=pair,
                side=side,
                volume=volume,
                avg_entry_price=entry_price,
                entry_time=current_time
            )
            
            logger.info(f"Opened {side} position in {pair}: "
                      f"{volume:.8f} @ {entry_price:.2f}")
        
        # Update position value tracking
        position_value = volume * entry_price
        self.max_position_value = max(self.max_position_value, position_value)
    
    def close_position(self, pair: str, volume: float, exit_price: float) -> Optional[Trade]:
        """Close a position (full or partial)"""
        if pair not in self.positions:
            logger.warning(f"Attempted to close non-existent position in {pair}")
            return None
        
        current_time = time.time()
        return self._close_partial_position(pair, volume, exit_price, current_time)
    
    def _close_partial_position(self, pair: str, volume: float, exit_price: float, 
                               exit_time: float) -> Optional[Trade]:
        """Close part or all of a position"""
        position = self.positions[pair]
        
        if volume >= position.volume:
            # Close entire position
            close_volume = position.volume
            del self.positions[pair]
        else:
            # Partial close
            close_volume = volume
            position.volume -= volume
        
        # Calculate P&L for closed portion
        if position.side == 'long':
            pnl = close_volume * (exit_price - position.avg_entry_price)
        else:  # short
            pnl = close_volume * (position.avg_entry_price - exit_price)
        
        # Create trade record
        trade = Trade(
            pair=pair,
            side=position.side,
            volume=close_volume,
            entry_price=position.avg_entry_price,
            exit_price=exit_price,
            entry_time=position.entry_time,
            exit_time=exit_time,
            pnl=pnl
        )
        
        self.trades.append(trade)
        self.total_realized_pnl += pnl
        
        # Update win/loss statistics
        if pnl > 0:
            self.win_count += 1
        else:
            self.loss_count += 1
        
        logger.info(f"Closed {position.side} position in {pair}: "
                  f"{close_volume:.8f} @ {exit_price:.2f} "
                  f"(P&L: ${pnl:.2f})")
        
        return trade
    
    def _calculate_unrealized_pnl(self, pair: str):
        """Calculate unrealized P&L for a position"""
        if pair not in self.positions or pair not in self.current_prices:
            return
        
        position = self.positions[pair]
        current_price = self.current_prices[pair]
        
        if position.side == 'long':
            unrealized_pnl = position.volume * (current_price - position.avg_entry_price)
        else:  # short
            unrealized_pnl = position.volume * (position.avg_entry_price - current_price)
        
        position.unrealized_pnl = unrealized_pnl
    
    def get_total_unrealized_pnl(self) -> float:
        """Get total unrealized P&L across all positions"""
        total = 0.0
        for position in self.positions.values():
            total += position.unrealized_pnl
        return total
    
    def get_total_pnl(self) -> float:
        """Get total P&L (realized + unrealized)"""
        return self.total_realized_pnl + self.get_total_unrealized_pnl()
    
    def get_win_rate(self) -> float:
        """Calculate win rate percentage"""
        total_trades = self.win_count + self.loss_count
        if total_trades == 0:
            return 0.0
        return (self.win_count / total_trades) * 100
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get comprehensive performance metrics"""
        total_unrealized = self.get_total_unrealized_pnl()
        total_pnl = self.get_total_pnl()
        
        return {
            'total_realized_pnl': self.total_realized_pnl,
            'total_unrealized_pnl': total_unrealized,
            'total_pnl': total_pnl,
            'win_rate': self.get_win_rate(),
            'total_trades': len(self.trades),
            'open_positions': len(self.positions),
            'max_drawdown': self.max_drawdown,
            'max_position_value': self.max_position_value
        }
    
    def get_position_summary(self) -> List[Dict]:
        """Get summary of all open positions"""
        summary = []
        for pair, position in self.positions.items():
            current_price = self.current_prices.get(pair, 0.0)
            
            summary.append({
                'pair': pair,
                'side': position.side,
                'volume': position.volume,
                'avg_entry_price': position.avg_entry_price,
                'current_price': current_price,
                'unrealized_pnl': position.unrealized_pnl,
                'position_value': position.volume * current_price,
                'entry_time': position.entry_time
            })
        
        return summary
    
    def update_daily_pnl(self):
        """Update daily P&L tracking for drawdown calculation"""
        current_time = time.time()
        total_pnl = self.get_total_pnl()
        
        self.daily_pnl.append((current_time, total_pnl))
        
        # Keep only last 30 days
        cutoff_time = current_time - (30 * 24 * 3600)
        self.daily_pnl = [(t, pnl) for t, pnl in self.daily_pnl if t > cutoff_time]
        
        # Update peak equity and max drawdown
        if total_pnl > self.peak_equity:
            self.peak_equity = total_pnl
        
        if self.peak_equity > 0:
            current_drawdown = (self.peak_equity - total_pnl) / self.peak_equity
            self.max_drawdown = max(self.max_drawdown, current_drawdown)

# Global P&L tracker instance
pnl_tracker = RealTimePnLTracker()
