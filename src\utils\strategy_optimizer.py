"""
Strategy Optimizer for Live Trading
Optimizes trading strategy parameters based on current market conditions.
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)

@dataclass
class MarketCondition:
    """Market condition analysis"""
    volatility: float
    trend_strength: float
    volume_profile: str  # 'low', 'normal', 'high'
    market_regime: str   # 'trending', 'ranging', 'volatile'
    
@dataclass
class OptimizedParameters:
    """Optimized strategy parameters"""
    ema_short: int
    ema_long: int
    rsi_period: int
    rsi_overbought: int
    rsi_oversold: int
    bollinger_period: int
    bollinger_std_dev: float
    volume_threshold: float
    risk_percentage: float

class StrategyOptimizer:
    """
    Optimizes trading strategy parameters based on real-time market analysis.
    
    Features:
    - Market condition detection (trending vs ranging)
    - Volatility-based parameter adjustment
    - Volume profile analysis
    - Dynamic risk adjustment
    """
    
    def __init__(self):
        self.price_history: Dict[str, List[float]] = {}
        self.volume_history: Dict[str, List[float]] = {}
        self.last_optimization: Dict[str, float] = {}
        self.optimization_interval = 3600  # 1 hour
        
        # Base parameters for different market conditions
        self.base_params = {
            'trending_low_vol': OptimizedParameters(
                ema_short=21, ema_long=50, rsi_period=14,
                rsi_overbought=75, rsi_oversold=25,
                bollinger_period=20, bollinger_std_dev=2.0,
                volume_threshold=1.2, risk_percentage=0.025
            ),
            'trending_high_vol': OptimizedParameters(
                ema_short=13, ema_long=34, rsi_period=10,
                rsi_overbought=80, rsi_oversold=20,
                bollinger_period=15, bollinger_std_dev=2.5,
                volume_threshold=1.8, risk_percentage=0.015
            ),
            'ranging_low_vol': OptimizedParameters(
                ema_short=50, ema_long=200, rsi_period=21,
                rsi_overbought=70, rsi_oversold=30,
                bollinger_period=25, bollinger_std_dev=1.8,
                volume_threshold=1.0, risk_percentage=0.02
            ),
            'ranging_high_vol': OptimizedParameters(
                ema_short=34, ema_long=89, rsi_period=14,
                rsi_overbought=65, rsi_oversold=35,
                bollinger_period=20, bollinger_std_dev=2.2,
                volume_threshold=1.5, risk_percentage=0.01
            )
        }
        
        logger.info("StrategyOptimizer initialized")
    
    def update_market_data(self, pair: str, price: float, volume: float):
        """Update market data for analysis"""
        if pair not in self.price_history:
            self.price_history[pair] = []
            self.volume_history[pair] = []
        
        self.price_history[pair].append(price)
        self.volume_history[pair].append(volume)
        
        # Keep only last 1000 data points
        if len(self.price_history[pair]) > 1000:
            self.price_history[pair] = self.price_history[pair][-1000:]
            self.volume_history[pair] = self.volume_history[pair][-1000:]
    
    def analyze_market_condition(self, pair: str) -> Optional[MarketCondition]:
        """Analyze current market conditions for a trading pair"""
        if pair not in self.price_history or len(self.price_history[pair]) < 50:
            return None
        
        prices = np.array(self.price_history[pair][-100:])  # Last 100 data points
        volumes = np.array(self.volume_history[pair][-100:])
        
        # Calculate volatility (standard deviation of returns)
        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns) * 100  # Percentage volatility
        
        # Calculate trend strength using ADX-like calculation
        high_low = np.abs(np.diff(prices))
        true_range = np.maximum(high_low, np.abs(prices[1:] - prices[:-1]))
        atr = np.mean(true_range[-14:])  # 14-period ATR
        
        # Directional movement
        up_moves = np.where(np.diff(prices) > 0, np.diff(prices), 0)
        down_moves = np.where(np.diff(prices) < 0, -np.diff(prices), 0)
        
        di_plus = np.mean(up_moves[-14:]) / atr if atr > 0 else 0
        di_minus = np.mean(down_moves[-14:]) / atr if atr > 0 else 0
        
        dx = abs(di_plus - di_minus) / (di_plus + di_minus) if (di_plus + di_minus) > 0 else 0
        trend_strength = dx * 100
        
        # Volume profile analysis
        avg_volume = np.mean(volumes)
        recent_volume = np.mean(volumes[-10:])
        volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
        
        if volume_ratio > 1.5:
            volume_profile = 'high'
        elif volume_ratio < 0.7:
            volume_profile = 'low'
        else:
            volume_profile = 'normal'
        
        # Market regime classification
        if trend_strength > 25:
            market_regime = 'trending'
        elif volatility > 3.0:
            market_regime = 'volatile'
        else:
            market_regime = 'ranging'
        
        return MarketCondition(
            volatility=volatility,
            trend_strength=trend_strength,
            volume_profile=volume_profile,
            market_regime=market_regime
        )
    
    def get_optimized_parameters(self, pair: str) -> Optional[OptimizedParameters]:
        """Get optimized parameters for current market conditions"""
        current_time = time.time()
        
        # Check if optimization is needed
        if (pair in self.last_optimization and 
            current_time - self.last_optimization[pair] < self.optimization_interval):
            return None
        
        market_condition = self.analyze_market_condition(pair)
        if not market_condition:
            return None
        
        # Select base parameters based on market condition
        vol_suffix = 'high_vol' if market_condition.volatility > 2.5 else 'low_vol'
        regime_key = f"{market_condition.market_regime}_{vol_suffix}"
        
        if regime_key not in self.base_params:
            regime_key = 'trending_low_vol'  # Default fallback
        
        base_params = self.base_params[regime_key]
        
        # Fine-tune parameters based on specific conditions
        optimized = OptimizedParameters(
            ema_short=base_params.ema_short,
            ema_long=base_params.ema_long,
            rsi_period=base_params.rsi_period,
            rsi_overbought=base_params.rsi_overbought,
            rsi_oversold=base_params.rsi_oversold,
            bollinger_period=base_params.bollinger_period,
            bollinger_std_dev=base_params.bollinger_std_dev,
            volume_threshold=base_params.volume_threshold,
            risk_percentage=base_params.risk_percentage
        )
        
        # Adjust for extreme volatility
        if market_condition.volatility > 5.0:
            optimized.risk_percentage *= 0.5  # Reduce risk in high volatility
            optimized.bollinger_std_dev *= 1.2  # Wider bands
        
        # Adjust for high volume
        if market_condition.volume_profile == 'high':
            optimized.volume_threshold *= 0.8  # Lower threshold when volume is high
        
        # Adjust RSI levels for trending markets
        if market_condition.trend_strength > 40:
            optimized.rsi_overbought = min(85, optimized.rsi_overbought + 5)
            optimized.rsi_oversold = max(15, optimized.rsi_oversold - 5)
        
        self.last_optimization[pair] = current_time
        
        logger.info(f"Optimized parameters for {pair}: "
                   f"Market={market_condition.market_regime}, "
                   f"Vol={market_condition.volatility:.2f}%, "
                   f"Trend={market_condition.trend_strength:.1f}, "
                   f"Risk={optimized.risk_percentage:.3f}")
        
        return optimized
    
    def get_market_summary(self) -> Dict[str, Dict]:
        """Get market condition summary for all pairs"""
        summary = {}
        
        for pair in self.price_history.keys():
            condition = self.analyze_market_condition(pair)
            if condition:
                summary[pair] = {
                    'volatility': condition.volatility,
                    'trend_strength': condition.trend_strength,
                    'volume_profile': condition.volume_profile,
                    'market_regime': condition.market_regime,
                    'data_points': len(self.price_history[pair])
                }
        
        return summary
    
    def should_halt_trading(self, pair: str) -> bool:
        """Determine if trading should be halted due to extreme conditions"""
        condition = self.analyze_market_condition(pair)
        if not condition:
            return False
        
        # Halt trading in extreme volatility
        if condition.volatility > 8.0:
            logger.warning(f"Extreme volatility detected for {pair}: {condition.volatility:.2f}%")
            return True
        
        # Halt trading if insufficient volume
        if condition.volume_profile == 'low' and condition.volatility > 4.0:
            logger.warning(f"Low volume + high volatility for {pair}")
            return True
        
        return False

# Global optimizer instance
strategy_optimizer = StrategyOptimizer()
