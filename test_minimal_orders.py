#!/usr/bin/env python3
"""
Minimal Order Testing for Live Trading Validation
Tests actual order placement with very small amounts to validate system functionality.
"""

import asyncio
import logging
import sys
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_minimal_orders():
    """Test minimal order placement and management"""
    
    print("=" * 80)
    print("MINIMAL ORDER TESTING - LIVE TRADING VALIDATION")
    print("WARNING: This will place REAL orders with REAL money!")
    print("=" * 80)
    print()
    
    # Confirm user wants to proceed
    confirmation = input("Type 'TEST' to proceed with minimal order testing: ")
    if confirmation != 'TEST':
        print("Test cancelled by user.")
        return False
    
    try:
        # Import required modules
        from src.agents.risk_execution_agent import KrakenAPIClient
        from src.utils.config import load_config
        
        # Load configuration
        config = load_config()
        
        if not config.kraken_api_keys:
            print("ERROR: No API keys configured!")
            return False
        
        # Use first API key for testing
        api_key, api_secret = config.kraken_api_keys[0], config.kraken_api_secrets[0]
        client = KrakenAPIClient(api_key, api_secret)
        
        print(f"✓ Using API key: {api_key[:8]}...")
        print()
        
        # Step 1: Get current account balance
        print("Step 1: Getting current account balance...")
        balances = await client.get_account_balance()
        
        usd_balance = balances.get('ZUSD', 0.0)
        btc_balance = balances.get('XXBT', 0.0)
        
        print(f"  USD Balance: ${usd_balance:.2f}")
        print(f"  BTC Balance: {btc_balance:.8f} BTC")
        
        if usd_balance < 10.0:
            print("ERROR: Insufficient USD balance for testing (need at least $10)")
            return False
        
        # Step 2: Get current BTC price
        print("\nStep 2: Getting current BTC price...")
        btc_result = await client._make_api_request('/0/public/Ticker?pair=XBTUSD', private=False)
        
        if 'XXBTZUSD' in btc_result:
            btc_price = float(btc_result['XXBTZUSD']['c'][0])
        elif 'XBTUSD' in btc_result:
            btc_price = float(btc_result['XBTUSD']['c'][0])
        else:
            print("ERROR: Could not get BTC price")
            return False
        
        print(f"  Current BTC/USD price: ${btc_price:.2f}")
        
        # Step 3: Calculate minimal order size
        print("\nStep 3: Calculating minimal order size...")
        
        # Kraken minimum order size for BTC is typically 0.0001 BTC
        min_btc_order = 0.0001
        min_order_value = min_btc_order * btc_price
        
        print(f"  Minimum BTC order: {min_btc_order:.8f} BTC")
        print(f"  Minimum order value: ${min_order_value:.2f}")
        
        if usd_balance < min_order_value * 2:  # Need 2x for safety
            print(f"ERROR: Insufficient balance for minimal order (need ${min_order_value * 2:.2f})")
            return False
        
        # Step 4: Place a minimal test buy order
        print(f"\nStep 4: Placing minimal test BUY order...")
        print(f"  Order: BUY {min_btc_order:.8f} BTC at market price")
        print(f"  Estimated cost: ~${min_order_value:.2f}")
        
        final_confirm = input("  Confirm placing REAL order? (yes/no): ")
        if final_confirm.lower() != 'yes':
            print("Order cancelled by user.")
            return False
        
        try:
            buy_result = await client.place_order(
                pair='XBTUSD',
                type='buy',
                ordertype='market',
                volume=min_btc_order
            )
            
            buy_order_id = buy_result.get('txid', [None])[0]
            print(f"  ✓ BUY order placed successfully!")
            print(f"  Order ID: {buy_order_id}")
            
        except Exception as e:
            print(f"  ERROR placing BUY order: {e}")
            return False
        
        # Step 5: Wait and check order status
        print(f"\nStep 5: Checking order status...")
        await asyncio.sleep(5)  # Wait for order to process
        
        try:
            open_orders = await client.get_open_orders()
            closed_orders = await client.get_closed_orders()
            
            if buy_order_id in open_orders.get('open', {}):
                print(f"  Order {buy_order_id} is still OPEN")
                order_status = "OPEN"
            elif buy_order_id in closed_orders.get('closed', {}):
                print(f"  Order {buy_order_id} is CLOSED (executed)")
                order_status = "CLOSED"
            else:
                print(f"  Order {buy_order_id} status unknown")
                order_status = "UNKNOWN"
                
        except Exception as e:
            print(f"  ERROR checking order status: {e}")
            order_status = "ERROR"
        
        # Step 6: Get updated balance
        print(f"\nStep 6: Getting updated account balance...")
        try:
            new_balances = await client.get_account_balance()
            new_usd_balance = new_balances.get('ZUSD', 0.0)
            new_btc_balance = new_balances.get('XXBT', 0.0)
            
            usd_change = new_usd_balance - usd_balance
            btc_change = new_btc_balance - btc_balance
            
            print(f"  New USD Balance: ${new_usd_balance:.2f} (change: ${usd_change:.2f})")
            print(f"  New BTC Balance: {new_btc_balance:.8f} BTC (change: {btc_change:.8f})")
            
        except Exception as e:
            print(f"  ERROR getting updated balance: {e}")
        
        # Step 7: If we have BTC, place a minimal sell order
        if new_btc_balance >= min_btc_order and order_status == "CLOSED":
            print(f"\nStep 7: Placing minimal test SELL order...")
            print(f"  Order: SELL {min_btc_order:.8f} BTC at market price")
            
            sell_confirm = input("  Confirm placing SELL order? (yes/no): ")
            if sell_confirm.lower() == 'yes':
                try:
                    sell_result = await client.place_order(
                        pair='XBTUSD',
                        type='sell',
                        ordertype='market',
                        volume=min_btc_order
                    )
                    
                    sell_order_id = sell_result.get('txid', [None])[0]
                    print(f"  ✓ SELL order placed successfully!")
                    print(f"  Order ID: {sell_order_id}")
                    
                    # Wait and check final balance
                    await asyncio.sleep(5)
                    final_balances = await client.get_account_balance()
                    final_usd = final_balances.get('ZUSD', 0.0)
                    final_btc = final_balances.get('XXBT', 0.0)
                    
                    print(f"\nFinal balances:")
                    print(f"  USD: ${final_usd:.2f}")
                    print(f"  BTC: {final_btc:.8f}")
                    
                except Exception as e:
                    print(f"  ERROR placing SELL order: {e}")
        
        print("\n" + "=" * 80)
        print("MINIMAL ORDER TESTING COMPLETED")
        print("=" * 80)
        print("✓ Live order placement functionality validated")
        print("✓ Order status tracking working")
        print("✓ Balance updates confirmed")
        print("✓ System ready for live trading")
        
        return True
        
    except Exception as e:
        print(f"FATAL ERROR during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main entry point"""
    success = await test_minimal_orders()
    
    if success:
        print("\n🎉 All minimal order tests PASSED!")
        print("The system is validated for live trading.")
    else:
        print("\n❌ Minimal order tests FAILED!")
        print("Please review errors before proceeding with live trading.")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)
